OCI_TENANCY_OCID	ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda
{
  "data": {
    "defined-tags": {},
    "description": "arthurlocke92",
    "freeform-tags": {},
    "home-region-key": "FRA",
    "id": "ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda",
    "name": "arthurlocke92",
    "upi-idcs-compatibility-layer-endpoint": null
  }
}


PS C:\Users\<USER>\Users\kushu> oci compute instance list --compartment-id ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda
{
  "data": [
    {
      "agent-config": {
        "are-all-plugins-disabled": false,
        "is-management-disabled": false,
        "is-monitoring-disabled": false,
        "plugins-config": null
      },
      "availability-config": {
        "is-live-migration-preferred": null,
        "recovery-action": "RESTORE_INSTANCE"
      },
      "availability-domain": "DkBA:EU-FRANKFURT-1-AD-1",
      "capacity-reservation-id": null,
      "cluster-placement-group-id": null,
      "compartment-id": "ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda",
      "compute-host-group-id": null,
      "dedicated-vm-host-id": null,
      "defined-tags": {
        "Oracle-Tags": {
          "CreatedBy": "ocid1.nodepool.oc1.eu-frankfurt-1.aaaaaaaa6ey4ipmhey6zl3wg6dwqc52qozhhlmcjpdjkpzfyonkllwqze2dq",
          "CreatedOn": "2025-06-30T06:48:11.409Z"
        }
      },
      "display-name": "oke-ci7fmrl6jya-nkllwqze2dq-slxou6ti5tq-0",
      "extended-metadata": {},
      "fault-domain": "FAULT-DOMAIN-1",
      "freeform-tags": {},
      "id": "ocid1.instance.oc1.eu-frankfurt-1.antheljrjgemlpycs5wcayztpvxg7yrroetgdsqmvdg7rqla5bqu5scg5dgq",
      "image-id": "ocid1.image.oc1.eu-frankfurt-1.aaaaaaaaxg25d2ph37ruwvqdwxfj2eskxwkgvgbxzgrdg7q6a4ajvbx7u55q",
      "instance-configuration-id": null,
      "instance-options": {
        "are-legacy-imds-endpoints-disabled": false
      },
      "ipxe-script": null,
      "is-cross-numa-node": false,
      "launch-mode": "PARAVIRTUALIZED",
      "launch-options": {
        "boot-volume-type": "PARAVIRTUALIZED",
        "firmware": "UEFI_64",
        "is-consistent-volume-naming-enabled": true,
        "is-pv-encryption-in-transit-enabled": false,
        "network-type": "PARAVIRTUALIZED",
        "remote-data-volume-type": "PARAVIRTUALIZED"
      },
      "licensing-configs": null,
      "lifecycle-state": "RUNNING",
      "metadata": {
        "ansible_args": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "bootstrap-kubelet-conf": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "inventory_tmpl": "[tkm-minions]\nlocalhost initial_node_labels=name=pool_free,oci.oraclecloud.com/node.info.managed=true node_is_on_private_subnet={NODE_IS_ON_PRIVATE_SUBNET} nodeid_suffix={NODE_SUFFIX} shape=VM.Standard.A1.Flex ansible_user=opc tenancyid=ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda compartmentid_prefix=ocid1.tenancy.oc1 k8s_kubelet_hostname={K8S_KUBELET_HOSTNAME} region_key=FRA ansible_connection=local public_ipaddress={PUBLIC_IP} zone=DkBA:EU-FRANKFURT-1-AD-1 k8s_version=v1.33.1 displayname=oke-ci7fmrl6jya-nkllwqze2dq-slxou6ti5tq-0 compartmentname=arthurlocke92 nodeid_prefix={NODE_PREFIX} region_name=eu-frankfurt-1 ta_version={TA_VERSION} region=eu-frankfurt-1 compartmentid_suffix=aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda private_ipaddress={PRIVATE_IP}\n",
        "ip-families": "IPv4",
        "oci_tld": "oraclecloud.com",
        "oke-ad": "DkBA:EU-FRANKFURT-1-AD-1",
        "oke-cluster-display-name": "cluster_basic_n8n",
        "oke-cluster-id": "ocid1.cluster.oc1.eu-frankfurt-1.aaaaaaaa52oqqmmeqltnyd4dw7q4bdvvimau7xmw762xryw7pci7fmrl6jya",
        "oke-cluster-label": "ci7fmrl6jya",
        "oke-compartment-name": "arthurlocke92",
        "oke-image-name": "Oracle-Linux-8.10-aarch64-2025.05.19-0-OKE-1.33.1-804",
        "oke-initial-node-labels": "name=pool_free,oci.oraclecloud.com/node.info.managed=true",
        "oke-is-cloud-init": "true",
        "oke-is-on-private-subnet": "false",
        "oke-is-onsr": "false",
        "oke-is-preemptible": "false",
        "oke-is-private-worker": "true",
        "oke-k8version": "v1.33.1",
        "oke-max-pods": "31",
        "oke-native-pod-networking": "true",
        "oke-node-package-node-all-version": "oci-oke-node-all-1.33.1*",
        "oke-pool-id": "ocid1.nodepool.oc1.eu-frankfurt-1.aaaaaaaa6ey4ipmhey6zl3wg6dwqc52qozhhlmcjpdjkpzfyonkllwqze2dq",
        "oke-pool-label": "nkllwqze2dq",
        "oke-slot": "0",
        "oke-subnet-label": "slxou6ti5tq",
        "oke-tenancy-id": "ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda",
        "oke-tm": "oke",
        "oke_artifact_signing_key": "-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAxjL/BsfETzecKeDbcZz0\nNvzZZVhNe4N259J44oZHtWWjep2gz8KSrbyw1S3TjlV1bgKVVqUNzB3uV51UVaSk\nEFAoqFc/K/f9KbjulxVbpe0ww99S36mvl/8gBb2Csbf3x9/j7H26e2PibT0CNdbW\n7ckIfSTtGAM1534OLFB6/jwXadHYr+7eGwE4X+wzoeukS8c7cTdq6fJomtFtHVfB\nL2h7cDowUhzzq4NYhdqiM/oKq6jGACntju0oOC/NOClrPW90VygpR+UWIbzNQeO2\nR9oQBJ+ih+mLlKs9AnBppuk3cG5dwvZ1Fh3MNaXdMW6dm8fA4XkZjvG+ZO2ZOMjk\nzxDBJYCrbgv/OUl275DE777QqCEC0NzXX/Lw1ryOZQpG8SW6k9Mn/dK0XtWWUhbs\nY5yJiP9aFS5pO03TTqqNrps94AVP3aDdYCXc+4sBQlwkc/j0TwiqDfjoHQr+uNdQ\n6Thz65fRSegvQu/rQIlxdkFs3HeL3bABdKQ10Nka9OFNqCiZuBtAIbwJ51PnhLbw\npbxnU642RKatN9Jy5U4SEY13wc7OMEDm+rUG7o6svO2kDu3D+FgVsSg10wp0xutJ\nWLSU3+4wfOQpCgoRBZW2hueEPQAse6ifjO/pVlNcZdLuWlH3CiGFJ/iFXv8GWr+M\nkWSI9Sbs3SP85iLVJQukptMCAwEAAQ==\n-----END PUBLIC KEY-----",
        "oke_init_script": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "oke_maturity": "prd",
        "oke_namespace": "odx-oke",
        "realm": "oc1",
        "ssh_authorized_keys": "",
        "user_data": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      },
      "placement-constraint-details": null,
      "platform-config": null,
      "preemptible-instance-config": null,
      "region": "eu-frankfurt-1",
      "security-attributes": {},
      "security-attributes-state": "STABLE",
      "shape": "VM.Standard.A1.Flex",
      "shape-config": {
        "baseline-ocpu-utilization": null,
        "gpu-description": null,
        "gpus": 0,
        "local-disk-description": null,
        "local-disks": 0,
        "local-disks-total-size-in-gbs": null,
        "max-vnic-attachments": 2,
        "memory-in-gbs": 6.0,
        "networking-bandwidth-in-gbps": 1.0,
        "ocpus": 1.0,
        "processor-description": "3.0 GHz Ampere® Altra™",
        "vcpus": 1
      },
      "source-details": {
        "boot-volume-size-in-gbs": null,
        "boot-volume-vpus-per-gb": null,
        "image-id": "ocid1.image.oc1.eu-frankfurt-1.aaaaaaaaxg25d2ph37ruwvqdwxfj2eskxwkgvgbxzgrdg7q6a4ajvbx7u55q",
        "instance-source-image-filter-details": null,
        "kms-key-id": null,
        "source-type": "image"
      },
      "system-tags": {
        "orcl-containerengine": {
          "Cluster": "ocid1.cluster.oc1.eu-frankfurt-1.aaaaaaaa52oqqmmeqltnyd4dw7q4bdvvimau7xmw762xryw7pci7fmrl6jya",
          "NodePool": "ocid1.nodepool.oc1.eu-frankfurt-1.aaaaaaaa6ey4ipmhey6zl3wg6dwqc52qozhhlmcjpdjkpzfyonkllwqze2dq",
          "NodeType": "oke-managed"
        }
      },
      "time-created": "2025-06-30T06:48:11.916000+00:00",
      "time-maintenance-reboot-due": null
    },
    {
      "agent-config": {
        "are-all-plugins-disabled": false,
        "is-management-disabled": false,
        "is-monitoring-disabled": false,
        "plugins-config": [
          {
            "desired-state": "DISABLED",
            "name": "Vulnerability Scanning"
          },
          {
            "desired-state": "DISABLED",
            "name": "Management Agent"
          },
          {
            "desired-state": "ENABLED",
            "name": "Custom Logs Monitoring"
          },
          {
            "desired-state": "DISABLED",
            "name": "Compute RDMA GPU Monitoring"
          },
          {
            "desired-state": "ENABLED",
            "name": "Compute Instance Monitoring"
          },
          {
            "desired-state": "DISABLED",
            "name": "Compute HPC RDMA Auto-Configuration"
          },
          {
            "desired-state": "DISABLED",
            "name": "Compute HPC RDMA Authentication"
          },
          {
            "desired-state": "ENABLED",
            "name": "Cloud Guard Workload Protection"
          },
          {
            "desired-state": "DISABLED",
            "name": "Block Volume Management"
          },
          {
            "desired-state": "DISABLED",
            "name": "Bastion"
          }
        ]
      },
      "availability-config": {
        "is-live-migration-preferred": null,
        "recovery-action": "RESTORE_INSTANCE"
      },
      "availability-domain": "DkBA:EU-FRANKFURT-1-AD-3",
      "capacity-reservation-id": null,
      "cluster-placement-group-id": null,
      "compartment-id": "ocid1.tenancy.oc1..aaaaaaaajus6xekdrxg7hh2xic3nc5xr4z7inlohvjkp2ja6tj3xzrym5oda",
      "compute-host-group-id": null,
      "dedicated-vm-host-id": null,
      "defined-tags": {
        "Oracle-Tags": {
          "CreatedBy": "default/<EMAIL>",
          "CreatedOn": "2025-06-23T19:08:36.092Z"
        }
      },
      "display-name": "vpn-instance",
      "extended-metadata": {},
      "fault-domain": "FAULT-DOMAIN-1",
      "freeform-tags": {},
      "id": "ocid1.instance.oc1.eu-frankfurt-1.antheljtjgemlpycn56ada7n3vyg7u4bmfy3i7j4ap5mlbmndzpak24spp5a",
      "image-id": "ocid1.image.oc1.eu-frankfurt-1.aaaaaaaapyern7on7cez3ajw564k6dthrojwwkccbl72etbetsio7byq35wa",
      "instance-configuration-id": null,
      "instance-options": {
        "are-legacy-imds-endpoints-disabled": false
      },
      "ipxe-script": null,
      "is-cross-numa-node": false,
      "launch-mode": "PARAVIRTUALIZED",
      "launch-options": {
        "boot-volume-type": "PARAVIRTUALIZED",
        "firmware": "UEFI_64",
        "is-consistent-volume-naming-enabled": true,
        "is-pv-encryption-in-transit-enabled": true,
        "network-type": "PARAVIRTUALIZED",
        "remote-data-volume-type": "PARAVIRTUALIZED"
      },
      "licensing-configs": null,
      "lifecycle-state": "RUNNING",
      "metadata": {
        "ssh_authorized_keys": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDHqKpT4qgEkMkHEtRsgpDuk6HQNGp5CBQw4vypslrlyeMv6DmXkkmifR1PwZr+bqG94hdNtFkn0qL6bHELFBcXPXLval8XZTDYsWTKaJ1eIWp2aYGRtB0UhGiwYSPQGfXZjiYZrUeqhDT7eS8UA6y6gfZhJVCf5VHiaLV0dzkFQ9oY50H1BwRJF1nFs4gEwOLpJrwiohec72OEEZEa9vCi8ty015IqjzTTs/nTfZexler3yMMwfXlua9Qv/EKFMrxMJNptAlPGoMUdyUSApPvlZHAb/UG0u3WnqtqYCmXbtjpA4X3PixKcbddiqferZNiTXT42WOjb39vA45WfxJC1 ssh-key-2025-06-23"
      },
      "placement-constraint-details": null,
      "platform-config": null,
      "preemptible-instance-config": null,
      "region": "eu-frankfurt-1",
      "security-attributes": {},
      "security-attributes-state": "STABLE",
      "shape": "VM.Standard.E2.1.Micro",
      "shape-config": {
        "baseline-ocpu-utilization": null,
        "gpu-description": null,
        "gpus": 0,
        "local-disk-description": null,
        "local-disks": 0,
        "local-disks-total-size-in-gbs": null,
        "max-vnic-attachments": 1,
        "memory-in-gbs": 1.0,
        "networking-bandwidth-in-gbps": 0.48,
        "ocpus": 1.0,
        "processor-description": "2.0 GHz AMD EPYC™ 7551 (Naples)",
        "vcpus": 2
      },
      "source-details": {
        "boot-volume-size-in-gbs": null,
        "boot-volume-vpus-per-gb": null,
        "image-id": "ocid1.image.oc1.eu-frankfurt-1.aaaaaaaapyern7on7cez3ajw564k6dthrojwwkccbl72etbetsio7byq35wa",
        "instance-source-image-filter-details": null,
        "kms-key-id": null,
        "source-type": "image"
      },
      "system-tags": {
        "orcl-cloud": {
          "free-tier-retained": "true"
        }
      },
      "time-created": "2025-06-23T19:08:36.605000+00:00",
      "time-maintenance-reboot-due": null
    }
  ]
}

PS C:\Users\<USER>